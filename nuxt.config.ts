// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  devServer: {
    port: 7536,
  },

  app: {
    head: {
      meta: [{ name: 'viewport', content: 'width=device-width, initial-scale=1' }],
    },
  },

  vite: {
    optimizeDeps: {
      include: []
    }
  },

  unocss: {
    nuxtLayers: true,
  },
  css: ['@unocss/reset/tailwind.css'],

  modules: ['@nuxt/eslint', '@nuxt/image', '@unocss/nuxt', '@element-plus/nuxt'],
});